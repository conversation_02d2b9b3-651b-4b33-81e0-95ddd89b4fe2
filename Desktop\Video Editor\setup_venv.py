#!/usr/bin/env python3
"""
Setup script for Video Editor Virtual Environment
"""
import subprocess
import sys
import os

def create_venv():
    """Create and setup virtual environment for the video editor"""
    print("🎬 Setting up Video Editor Virtual Environment...")
    
    # Create virtual environment
    print("📦 Creating virtual environment...")
    subprocess.run([sys.executable, "-m", "venv", "video_editor_env"], check=True)
    
    # Determine the correct path for the virtual environment
    if os.name == 'nt':  # Windows
        pip_path = os.path.join("video_editor_env", "Scripts", "pip")
        python_path = os.path.join("video_editor_env", "Scripts", "python")
    else:  # Unix/Linux/MacOS
        pip_path = os.path.join("video_editor_env", "bin", "pip")
        python_path = os.path.join("video_editor_env", "bin", "python")
    
    # Upgrade pip
    print("⬆️  Upgrading pip...")
    subprocess.run([python_path, "-m", "pip", "install", "--upgrade", "pip"], check=True)
    
    # Install requirements
    print("📚 Installing requirements...")
    subprocess.run([pip_path, "install", "-r", "requirements.txt"], check=True)
    
    print("✅ Virtual environment setup complete!")
    print("\n🚀 To activate the environment:")
    if os.name == 'nt':
        print("   video_editor_env\\Scripts\\activate")
    else:
        print("   source video_editor_env/bin/activate")
    print("\n🎥 To run the video editor:")
    print("   python main.py")

if __name__ == "__main__":
    create_venv()