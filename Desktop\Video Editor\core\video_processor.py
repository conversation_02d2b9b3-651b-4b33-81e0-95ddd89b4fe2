"""
Video processing engine for the Video Editor
Handles video loading, trimming, speed adjustments, and export
"""

import logging
import numpy as np
from pathlib import Path
from typing import List, Tuple, Optional, Dict, Any
import threading
import time

try:
    import cv2
    from moviepy import <PERSON>FileClip, CompositeVideoClip, concatenate_videoclips
    from moviepy.vfx import MultiplySpeed
    MOVIEPY_AVAILABLE = True
except ImportError as e:
    logging.error(f"Video processing dependencies not available: {e}")
    MOVIEPY_AVAILABLE = False
    # Create dummy classes for type hints
    class VideoFileClip:
        pass
    class MultiplySpeed:
        pass

class VideoSegment:
    """Represents a video segment with timing and effects"""

    def __init__(self, start: float, end: float, speed: float = 1.0):
        self.start = start  # Start time in seconds
        self.end = end      # End time in seconds
        self.speed = speed  # Speed multiplier (1.0 = normal, 2.0 = 2x speed)
        self.effects = {}   # Additional effects

    @property
    def duration(self) -> float:
        """Get segment duration"""
        return self.end - self.start

    @property
    def output_duration(self) -> float:
        """Get output duration after speed adjustment"""
        return self.duration / self.speed

    def __repr__(self):
        return f"VideoSegment({self.start:.2f}-{self.end:.2f}, {self.speed}x)"

class VideoProcessor:
    """Main video processing engine"""

    def __init__(self):
        self.video_clip = None
        self.original_path = None
        self.segments: List[VideoSegment] = []
        self.preview_frame = None
        self.is_processing = False

        # Processing callbacks
        self.progress_callback = None
        self.completion_callback = None

    def load_video(self, file_path: str) -> bool:
        """Load a video file"""
        try:
            self.original_path = file_path
            self.video_clip = VideoFileClip(file_path)
            self.segments = []

            logging.info(f"Loaded video: {file_path}")
            logging.info(f"Duration: {self.video_clip.duration:.2f}s")
            logging.info(f"FPS: {self.video_clip.fps}")
            logging.info(f"Size: {self.video_clip.size}")

            return True

        except Exception as e:
            logging.error(f"Error loading video: {e}")
            return False

    def get_video_info(self) -> Optional[Dict[str, Any]]:
        """Get video information"""
        if not self.video_clip:
            return None

        return {
            'duration': self.video_clip.duration,
            'fps': self.video_clip.fps,
            'size': self.video_clip.size,
            'filename': Path(self.original_path).name if self.original_path else "Unknown"
        }

    def add_segment(self, start: float, end: float, speed: float = 1.0) -> VideoSegment:
        """Add a video segment"""
        segment = VideoSegment(start, end, speed)
        self.segments.append(segment)
        logging.info(f"Added segment: {segment}")
        return segment

    def remove_segment(self, segment: VideoSegment) -> bool:
        """Remove a video segment"""
        try:
            self.segments.remove(segment)
            logging.info(f"Removed segment: {segment}")
            return True
        except ValueError:
            return False

    def clear_segments(self):
        """Clear all segments"""
        self.segments.clear()
        logging.info("Cleared all segments")

    def get_frame_at_time(self, time_seconds: float) -> Optional[np.ndarray]:
        """Get a frame at specific time"""
        if not self.video_clip:
            return None

        try:
            # Clamp time to video duration
            time_seconds = max(0, min(time_seconds, self.video_clip.duration))
            frame = self.video_clip.get_frame(time_seconds)
            return frame
        except Exception as e:
            logging.error(f"Error getting frame at {time_seconds}s: {e}")
            return None

    def create_preview_clip(self) -> Optional[VideoFileClip]:
        """Create a preview clip from segments"""
        if not self.video_clip or not self.segments:
            return None

        try:
            clips = []

            for segment in self.segments:
                # Extract segment
                clip_segment = self.video_clip.subclip(segment.start, segment.end)

                # Apply speed effect
                if segment.speed != 1.0:
                    clip_segment = speedx(clip_segment, factor=segment.speed)

                clips.append(clip_segment)

            # Concatenate all clips
            if len(clips) == 1:
                return clips[0]
            else:
                return concatenate_videoclips(clips)

        except Exception as e:
            logging.error(f"Error creating preview clip: {e}")
            return None

    def export_video(self, output_path: str, quality: str = "medium") -> bool:
        """Export the edited video"""
        if not self.video_clip or not self.segments:
            logging.error("No video loaded or no segments defined")
            return False

        def export_thread():
            try:
                self.is_processing = True

                # Create final clip
                final_clip = self.create_preview_clip()
                if not final_clip:
                    raise Exception("Failed to create final clip")

                # Quality settings
                quality_settings = {
                    "low": {"bitrate": "500k", "fps": 24},
                    "medium": {"bitrate": "1500k", "fps": 30},
                    "high": {"bitrate": "5000k", "fps": 60}
                }

                settings = quality_settings.get(quality, quality_settings["medium"])

                # Export with progress callback
                def progress_callback(t):
                    if self.progress_callback:
                        progress = t / final_clip.duration if final_clip.duration > 0 else 0
                        self.progress_callback(progress)

                final_clip.write_videofile(
                    output_path,
                    bitrate=settings["bitrate"],
                    fps=settings["fps"],
                    logger=None,  # Suppress moviepy logs
                    verbose=False,
                    progress_bar=False,
                    audio_codec='aac'
                )

                # Cleanup
                final_clip.close()

                self.is_processing = False

                if self.completion_callback:
                    self.completion_callback(True, output_path)

                logging.info(f"Video exported successfully: {output_path}")
                return True

            except Exception as e:
                self.is_processing = False
                logging.error(f"Export failed: {e}")

                if self.completion_callback:
                    self.completion_callback(False, str(e))

                return False

        # Start export in background thread
        export_thread_obj = threading.Thread(target=export_thread)
        export_thread_obj.daemon = True
        export_thread_obj.start()

        return True

    def set_progress_callback(self, callback):
        """Set progress callback for export"""
        self.progress_callback = callback

    def set_completion_callback(self, callback):
        """Set completion callback for export"""
        self.completion_callback = callback

    def cleanup(self):
        """Cleanup resources"""
        if self.video_clip:
            self.video_clip.close()
            self.video_clip = None

        self.segments.clear()
        logging.info("Video processor cleaned up")
