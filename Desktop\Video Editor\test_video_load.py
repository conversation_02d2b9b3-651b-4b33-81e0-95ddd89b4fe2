#!/usr/bin/env python3
"""
Test script to verify video loading functionality
"""

import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_moviepy_import():
    """Test MoviePy imports"""
    try:
        from moviepy import VideoFileClip
        print("✅ MoviePy VideoFileClip import successful")
        return True
    except ImportError as e:
        print(f"❌ MoviePy import failed: {e}")
        return False

def test_video_processor():
    """Test video processor"""
    try:
        from core.video_processor import VideoProcessor
        processor = VideoProcessor()
        print("✅ VideoProcessor created successfully")
        return processor
    except Exception as e:
        print(f"❌ VideoProcessor creation failed: {e}")
        return None

def test_video_loading(processor, video_path):
    """Test loading a specific video file"""
    if not processor:
        return False
        
    try:
        print(f"🎬 Attempting to load: {video_path}")
        success = processor.load_video(video_path)
        if success:
            print(f"✅ Video loaded successfully!")
            info = processor.get_video_info()
            if info:
                print(f"   Duration: {info['duration']:.2f}s")
                print(f"   FPS: {info['fps']:.2f}")
                print(f"   Size: {info['size']}")
            return True
        else:
            print(f"❌ Failed to load video")
            return False
    except Exception as e:
        print(f"❌ Error during video loading: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Video Editor Components...")
    print("=" * 50)
    
    # Test imports
    if not test_moviepy_import():
        print("Cannot proceed without MoviePy")
        return
    
    # Test processor creation
    processor = test_video_processor()
    if not processor:
        print("Cannot proceed without VideoProcessor")
        return
    
    # Test with a sample video (if provided)
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        if Path(video_path).exists():
            test_video_loading(processor, video_path)
        else:
            print(f"❌ Video file not found: {video_path}")
    else:
        print("💡 To test video loading, run: python test_video_load.py <path_to_video.mp4>")
    
    print("=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
