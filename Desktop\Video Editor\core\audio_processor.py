"""
Audio processing engine with keyframe support
Handles audio loading, mixing, effects, and keyframe-based automation
"""

import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import threading

try:
    import librosa
    import soundfile as sf
    from moviepy import AudioFileClip, CompositeAudioClip
    AUDIO_DEPS_AVAILABLE = True
except ImportError as e:
    logging.error(f"Audio processing dependencies not available: {e}")
    AUDIO_DEPS_AVAILABLE = False
    # Create dummy classes for type hints
    class AudioFileClip:
        pass

class AudioKeyframe:
    """Represents an audio keyframe for automation"""

    def __init__(self, time: float, parameter: str, value: float):
        self.time = time          # Time in seconds
        self.parameter = parameter # Parameter name (volume, pan, etc.)
        self.value = value        # Parameter value

    def __repr__(self):
        return f"AudioKeyframe({self.time:.2f}s, {self.parameter}={self.value})"

class AudioTrack:
    """Represents an audio track with keyframes and effects"""

    def __init__(self, name: str, file_path: Optional[str] = None):
        self.name = name
        self.file_path = file_path
        self.audio_clip = None
        self.keyframes: List[AudioKeyframe] = []
        self.start_time = 0.0
        self.volume = 1.0
        self.pan = 0.0  # -1.0 (left) to 1.0 (right)
        self.muted = False
        self.solo = False

        if file_path:
            self.load_audio(file_path)

    def load_audio(self, file_path: str) -> bool:
        """Load audio file"""
        if not AUDIO_DEPS_AVAILABLE:
            logging.error("Audio dependencies not available - cannot load audio")
            return False

        try:
            self.file_path = file_path
            self.audio_clip = AudioFileClip(filename=file_path)
            logging.info(f"Loaded audio: {file_path}")
            return True
        except Exception as e:
            logging.error(f"Error loading audio {file_path}: {e}")
            return False

    def add_keyframe(self, time: float, parameter: str, value: float):
        """Add a keyframe"""
        keyframe = AudioKeyframe(time, parameter, value)

        # Remove existing keyframe at same time for same parameter
        self.keyframes = [kf for kf in self.keyframes
                         if not (kf.time == time and kf.parameter == parameter)]

        self.keyframes.append(keyframe)
        self.keyframes.sort(key=lambda kf: kf.time)

        logging.info(f"Added keyframe: {keyframe}")

    def remove_keyframe(self, keyframe: AudioKeyframe) -> bool:
        """Remove a keyframe"""
        try:
            self.keyframes.remove(keyframe)
            logging.info(f"Removed keyframe: {keyframe}")
            return True
        except ValueError:
            return False

    def get_parameter_at_time(self, time: float, parameter: str) -> float:
        """Get interpolated parameter value at specific time"""
        # Get keyframes for this parameter
        param_keyframes = [kf for kf in self.keyframes if kf.parameter == parameter]

        if not param_keyframes:
            # Return default values
            defaults = {"volume": self.volume, "pan": self.pan}
            return defaults.get(parameter, 0.0)

        # Sort by time
        param_keyframes.sort(key=lambda kf: kf.time)

        # Find surrounding keyframes
        before = None
        after = None

        for kf in param_keyframes:
            if kf.time <= time:
                before = kf
            elif kf.time > time and after is None:
                after = kf
                break

        # Interpolate between keyframes
        if before and after:
            # Linear interpolation
            t = (time - before.time) / (after.time - before.time)
            return before.value + (after.value - before.value) * t
        elif before:
            return before.value
        elif after:
            return after.value
        else:
            defaults = {"volume": self.volume, "pan": self.pan}
            return defaults.get(parameter, 0.0)

    def get_duration(self) -> float:
        """Get audio duration"""
        if self.audio_clip:
            return self.audio_clip.duration
        return 0.0

    def cleanup(self):
        """Cleanup resources"""
        if self.audio_clip:
            self.audio_clip.close()
            self.audio_clip = None

class AudioProcessor:
    """Main audio processing engine"""

    def __init__(self):
        self.tracks: List[AudioTrack] = []
        self.master_volume = 1.0
        self.sample_rate = 44100
        self.is_processing = False

        # Processing callbacks
        self.progress_callback = None
        self.completion_callback = None

    def add_track(self, name: str, file_path: Optional[str] = None) -> AudioTrack:
        """Add a new audio track"""
        track = AudioTrack(name, file_path)
        self.tracks.append(track)
        logging.info(f"Added audio track: {name}")
        return track

    def remove_track(self, track: AudioTrack) -> bool:
        """Remove an audio track"""
        try:
            track.cleanup()
            self.tracks.remove(track)
            logging.info(f"Removed audio track: {track.name}")
            return True
        except ValueError:
            return False

    def get_track_by_name(self, name: str) -> Optional[AudioTrack]:
        """Get track by name"""
        for track in self.tracks:
            if track.name == name:
                return track
        return None

    def analyze_audio(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Analyze audio file and extract features"""
        try:
            # Load audio with librosa
            y, sr = librosa.load(file_path, sr=self.sample_rate)

            # Extract features
            tempo, beats = librosa.beat.beat_track(y=y, sr=sr)
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)

            # RMS energy for volume analysis
            rms = librosa.feature.rms(y=y)[0]

            return {
                'duration': len(y) / sr,
                'sample_rate': sr,
                'tempo': float(tempo),
                'beats': beats.tolist(),
                'spectral_centroid_mean': float(np.mean(spectral_centroids)),
                'rms_mean': float(np.mean(rms)),
                'rms_max': float(np.max(rms)),
                'mfcc_mean': np.mean(mfccs, axis=1).tolist()
            }

        except Exception as e:
            logging.error(f"Error analyzing audio {file_path}: {e}")
            return None

    def create_auto_keyframes(self, track: AudioTrack, effect_type: str = "volume_beats"):
        """Automatically create keyframes based on audio analysis"""
        if not track.file_path:
            return

        analysis = self.analyze_audio(track.file_path)
        if not analysis:
            return

        if effect_type == "volume_beats":
            # Create volume keyframes on beat positions
            beats = analysis.get('beats', [])
            base_volume = track.volume

            for i, beat_time in enumerate(beats):
                # Alternate between high and low volume on beats
                volume = base_volume * (1.2 if i % 2 == 0 else 0.8)
                track.add_keyframe(beat_time, "volume", volume)

        elif effect_type == "volume_energy":
            # Create volume keyframes based on RMS energy
            try:
                y, sr = librosa.load(track.file_path, sr=self.sample_rate)
                rms = librosa.feature.rms(y=y, hop_length=sr//10)[0]  # 10 FPS

                for i, energy in enumerate(rms):
                    time = i * 0.1  # 10 FPS = 0.1s intervals
                    # Normalize energy to volume range
                    volume = track.volume * (0.5 + energy / np.max(rms) * 0.5)
                    track.add_keyframe(time, "volume", volume)

            except Exception as e:
                logging.error(f"Error creating energy keyframes: {e}")

    def mix_audio(self, duration: float) -> Optional[np.ndarray]:
        """Mix all tracks into final audio"""
        if not self.tracks:
            return None

        try:
            # Create empty audio buffer
            samples = int(duration * self.sample_rate)
            mixed_audio = np.zeros((samples, 2))  # Stereo

            for track in self.tracks:
                if track.muted or not track.audio_clip:
                    continue

                # Skip if solo tracks exist and this isn't solo
                solo_tracks = [t for t in self.tracks if t.solo]
                if solo_tracks and not track.solo:
                    continue

                # Get track audio
                track_audio = track.audio_clip.to_soundarray(fps=self.sample_rate)

                # Ensure stereo
                if track_audio.ndim == 1:
                    track_audio = np.column_stack([track_audio, track_audio])

                # Calculate start and end samples
                start_sample = int(track.start_time * self.sample_rate)
                end_sample = min(start_sample + len(track_audio), samples)

                if start_sample >= samples:
                    continue

                # Apply keyframe automation
                track_length = end_sample - start_sample
                for i in range(track_length):
                    time = (start_sample + i) / self.sample_rate

                    # Get automated parameters
                    volume = track.get_parameter_at_time(time, "volume")
                    pan = track.get_parameter_at_time(time, "pan")

                    # Apply volume
                    sample = track_audio[i] * volume * self.master_volume

                    # Apply panning
                    left_gain = 1.0 - max(0, pan)
                    right_gain = 1.0 + min(0, pan)

                    mixed_audio[start_sample + i, 0] += sample[0] * left_gain
                    mixed_audio[start_sample + i, 1] += sample[1] * right_gain

            # Normalize to prevent clipping
            max_val = np.max(np.abs(mixed_audio))
            if max_val > 1.0:
                mixed_audio /= max_val

            return mixed_audio

        except Exception as e:
            logging.error(f"Error mixing audio: {e}")
            return None

    def export_audio(self, output_path: str, duration: float) -> bool:
        """Export mixed audio to file"""
        try:
            mixed_audio = self.mix_audio(duration)
            if mixed_audio is None:
                return False

            sf.write(output_path, mixed_audio, self.sample_rate)
            logging.info(f"Audio exported: {output_path}")
            return True

        except Exception as e:
            logging.error(f"Error exporting audio: {e}")
            return False

    def cleanup(self):
        """Cleanup all resources"""
        for track in self.tracks:
            track.cleanup()
        self.tracks.clear()
        logging.info("Audio processor cleaned up")
