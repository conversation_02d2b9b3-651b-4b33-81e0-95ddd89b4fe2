"""
Main application window for the Video Editor
Modern GUI using CustomTkinter with dark theme
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import customtkinter as ctk
from pathlib import Path
import logging
from typing import Optional

from core.video_processor import VideoProcessor
from core.audio_processor import AudioProcessor
from gui.timeline import TimelineWidget
from gui.preview_panel import PreviewPanel
from gui.effects_panel import EffectsPanel
from utils.helpers import format_time

class VideoEditorApp:
    """Main Video Editor Application"""

    def __init__(self):
        # Initialize processors
        self.video_processor = VideoProcessor()
        self.audio_processor = AudioProcessor()

        # Current project state
        self.current_video_path = None
        self.project_modified = False

        # Create main window
        self.root = ctk.CTk()
        self.setup_window()
        self.create_widgets()
        self.setup_bindings()

        logging.info("Video Editor application initialized")

    def setup_window(self):
        """Setup main window properties"""
        self.root.title("Modern Video Editor")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 600)

        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # Set icon (if available)
        try:
            icon_path = Path("assets/icon.ico")
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass

    def create_widgets(self):
        """Create and layout all GUI widgets"""

        # Create menu bar
        self.create_menu_bar()

        # Create toolbar
        self.create_toolbar()

        # Create main content area
        self.create_main_content()

        # Create status bar
        self.create_status_bar()

    def create_menu_bar(self):
        """Create menu bar"""
        # Note: CustomTkinter doesn't have native menu support
        # We'll create a custom menu bar using frames and buttons

        self.menu_frame = ctk.CTkFrame(self.root, height=40)
        self.menu_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=5, pady=2)
        self.menu_frame.grid_columnconfigure(0, weight=1)

        # File menu
        self.file_btn = ctk.CTkButton(
            self.menu_frame,
            text="File",
            width=60,
            command=self.show_file_menu
        )
        self.file_btn.grid(row=0, column=0, padx=5, pady=5, sticky="w")

        # Edit menu
        self.edit_btn = ctk.CTkButton(
            self.menu_frame,
            text="Edit",
            width=60,
            command=self.show_edit_menu
        )
        self.edit_btn.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        # Export menu
        self.export_btn = ctk.CTkButton(
            self.menu_frame,
            text="Export",
            width=60,
            command=self.show_export_menu
        )
        self.export_btn.grid(row=0, column=2, padx=5, pady=5, sticky="w")

    def create_toolbar(self):
        """Create toolbar with common actions"""
        self.toolbar_frame = ctk.CTkFrame(self.root, height=50)
        self.toolbar_frame.grid(row=1, column=0, columnspan=3, sticky="ew", padx=5, pady=2)

        # Open video button
        self.open_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="📁 Open Video",
            command=self.open_video,
            width=120
        )
        self.open_btn.grid(row=0, column=0, padx=5, pady=10)

        # Add audio button
        self.add_audio_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="🎵 Add Audio",
            command=self.add_audio_track,
            width=120
        )
        self.add_audio_btn.grid(row=0, column=1, padx=5, pady=10)

        # Export button
        self.export_video_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="💾 Export",
            command=self.export_video,
            width=120,
            state="disabled"
        )
        self.export_video_btn.grid(row=0, column=2, padx=5, pady=10)

        # Spacer
        spacer = ctk.CTkLabel(self.toolbar_frame, text="")
        spacer.grid(row=0, column=3, padx=20)

        # Playback controls
        self.play_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="▶️",
            command=self.toggle_playback,
            width=50
        )
        self.play_btn.grid(row=0, column=4, padx=2, pady=10)

        self.stop_btn = ctk.CTkButton(
            self.toolbar_frame,
            text="⏹️",
            command=self.stop_playback,
            width=50
        )
        self.stop_btn.grid(row=0, column=5, padx=2, pady=10)

    def create_main_content(self):
        """Create main content area with panels"""

        # Main content frame
        self.content_frame = ctk.CTkFrame(self.root)
        self.content_frame.grid(row=2, column=0, columnspan=3, sticky="nsew", padx=5, pady=5)
        self.content_frame.grid_columnconfigure(1, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)

        # Left panel - Effects and controls
        self.left_panel = ctk.CTkFrame(self.content_frame, width=300)
        self.left_panel.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)
        self.left_panel.grid_propagate(False)

        # Center panel - Preview and timeline
        self.center_panel = ctk.CTkFrame(self.content_frame)
        self.center_panel.grid(row=0, column=1, sticky="nsew", padx=5, pady=5)
        self.center_panel.grid_columnconfigure(0, weight=1)
        self.center_panel.grid_rowconfigure(0, weight=1)

        # Create preview panel
        self.preview_panel = PreviewPanel(self.center_panel, self.video_processor)
        self.preview_panel.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

        # Create timeline at bottom of center panel
        try:
            self.timeline = TimelineWidget(self.center_panel, self.video_processor, self.audio_processor)
            self.timeline.grid(row=1, column=0, sticky="ew", padx=5, pady=5)
        except Exception as e:
            logging.error(f"Error creating timeline: {e}")
            # Create placeholder
            self.timeline_placeholder = ctk.CTkLabel(self.center_panel, text="Timeline (Loading...)")
            self.timeline_placeholder.grid(row=1, column=0, sticky="ew", padx=5, pady=5)

        # Create effects panel in left panel
        self.effects_panel = EffectsPanel(self.left_panel, self.video_processor, self.audio_processor)
        self.effects_panel.grid(row=0, column=0, sticky="nsew", padx=5, pady=5)

    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = ctk.CTkFrame(self.root, height=30)
        self.status_frame.grid(row=3, column=0, columnspan=3, sticky="ew", padx=5, pady=2)

        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            anchor="w"
        )
        self.status_label.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        # Progress bar for exports
        self.progress_bar = ctk.CTkProgressBar(self.status_frame)
        self.progress_bar.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
        self.progress_bar.set(0)

        self.status_frame.grid_columnconfigure(1, weight=1)

    def setup_bindings(self):
        """Setup event bindings"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Keyboard shortcuts
        self.root.bind("<Control-o>", lambda e: self.open_video())
        self.root.bind("<Control-s>", lambda e: self.save_project())
        self.root.bind("<Control-e>", lambda e: self.export_video())
        self.root.bind("<space>", lambda e: self.toggle_playback())

    # Menu callbacks
    def show_file_menu(self):
        """Show file menu options"""
        # Create popup menu
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="Open Video...", command=self.open_video)
        menu.add_command(label="Save Project", command=self.save_project)
        menu.add_command(label="Load Project", command=self.load_project)
        menu.add_separator()
        menu.add_command(label="Exit", command=self.on_closing)

        # Show menu at button position
        x = self.file_btn.winfo_rootx()
        y = self.file_btn.winfo_rooty() + self.file_btn.winfo_height()
        menu.post(x, y)

    def show_edit_menu(self):
        """Show edit menu options"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="Clear All Segments", command=self.clear_segments)
        menu.add_command(label="Auto-detect Highlights", command=self.auto_detect_highlights)

        x = self.edit_btn.winfo_rootx()
        y = self.edit_btn.winfo_rooty() + self.edit_btn.winfo_height()
        menu.post(x, y)

    def show_export_menu(self):
        """Show export menu options"""
        menu = tk.Menu(self.root, tearoff=0)
        menu.add_command(label="Export Video...", command=self.export_video)
        menu.add_command(label="Export Audio Only...", command=self.export_audio)

        x = self.export_btn.winfo_rootx()
        y = self.export_btn.winfo_rooty() + self.export_btn.winfo_height()
        menu.post(x, y)

    # Core functionality
    def open_video(self):
        """Open video file"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.update_status("Loading video...")
            if self.video_processor.load_video(file_path):
                self.current_video_path = file_path
                self.update_status(f"Loaded: {Path(file_path).name}")
                self.export_video_btn.configure(state="normal")

                # Update preview and timeline
                self.preview_panel.video_loaded()
                if hasattr(self, 'timeline'):
                    self.timeline.video_loaded()

                self.project_modified = True
            else:
                error_msg = f"Failed to load video file: {Path(file_path).name}\n\nPossible causes:\n- Unsupported format\n- Corrupted file\n- Missing codecs\n\nTry converting to MP4 format."
                messagebox.showerror("Video Load Error", error_msg)
                self.update_status("Failed to load video")

    def add_audio_track(self):
        """Add audio track"""
        file_path = filedialog.askopenfilename(
            title="Select Audio File",
            filetypes=[
                ("Audio files", "*.mp3 *.wav *.aac *.ogg *.flac"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            track_name = Path(file_path).stem
            track = self.audio_processor.add_track(track_name, file_path)
            if track:
                self.update_status(f"Added audio track: {track_name}")
                self.timeline.audio_track_added(track)
                self.project_modified = True

    def export_video(self):
        """Export final video"""
        if not self.video_processor.video_clip:
            messagebox.showwarning("Warning", "No video loaded")
            return

        if not self.video_processor.segments:
            messagebox.showwarning("Warning", "No segments defined")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export Video",
            defaultextension=".mp4",
            filetypes=[
                ("MP4 files", "*.mp4"),
                ("AVI files", "*.avi"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self.start_export(file_path)

    def start_export(self, output_path: str):
        """Start video export process"""
        self.update_status("Exporting video...")
        self.progress_bar.set(0)

        # Setup callbacks
        self.video_processor.set_progress_callback(self.export_progress)
        self.video_processor.set_completion_callback(self.export_complete)

        # Start export
        self.video_processor.export_video(output_path)

    def export_progress(self, progress: float):
        """Update export progress"""
        self.progress_bar.set(progress)
        self.update_status(f"Exporting... {progress*100:.1f}%")
        self.root.update_idletasks()

    def export_complete(self, success: bool, result: str):
        """Handle export completion"""
        if success:
            self.update_status(f"Export complete: {Path(result).name}")
            messagebox.showinfo("Success", f"Video exported successfully!\n{result}")
        else:
            self.update_status("Export failed")
            messagebox.showerror("Error", f"Export failed: {result}")

        self.progress_bar.set(0)

    # Playback controls
    def toggle_playback(self):
        """Toggle video playback"""
        self.preview_panel.toggle_playback()

    def stop_playback(self):
        """Stop video playback"""
        self.preview_panel.stop_playback()

    # Project management
    def save_project(self):
        """Save current project"""
        # TODO: Implement project saving
        self.update_status("Project save not implemented yet")

    def load_project(self):
        """Load project file"""
        # TODO: Implement project loading
        self.update_status("Project load not implemented yet")

    def clear_segments(self):
        """Clear all video segments"""
        self.video_processor.clear_segments()
        self.timeline.refresh()
        self.project_modified = True
        self.update_status("Cleared all segments")

    def auto_detect_highlights(self):
        """Auto-detect video highlights"""
        # TODO: Implement highlight detection
        self.update_status("Auto-detect not implemented yet")

    def export_audio(self):
        """Export audio only"""
        # TODO: Implement audio export
        self.update_status("Audio export not implemented yet")

    # Utility methods
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.configure(text=message)
        logging.info(f"Status: {message}")

    def on_closing(self):
        """Handle application closing"""
        if self.project_modified:
            result = messagebox.askyesnocancel(
                "Save Project",
                "Do you want to save your project before closing?"
            )
            if result is None:  # Cancel
                return
            elif result:  # Yes
                self.save_project()

        # Cleanup
        self.video_processor.cleanup()
        self.audio_processor.cleanup()

        self.root.destroy()

    def run(self):
        """Start the application"""
        self.root.mainloop()
